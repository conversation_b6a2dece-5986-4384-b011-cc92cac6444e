using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Deployments;
using UNI.Master.Model.Deployments;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1.Deployments
{
    /// <summary>
    /// Deployment Database Controller
    /// </summary>
    [Route("api/v1/deployment-database/[action]")]
    [Authorize]
    public class DeploymentDatabaseController : UniController
    {
        private readonly IDeploymentDatabaseService _deploymentDatabaseService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="deploymentDatabaseService">Deployment database service</param>
        /// <param name="appSettings">Application settings</param>
        /// <param name="logger">Logger factory</param>
        public DeploymentDatabaseController(
            IDeploymentDatabaseService deploymentDatabaseService,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger) : base(appSettings, logger)
        {
            _deploymentDatabaseService = deploymentDatabaseService;
        }

        /// <summary>
        /// Get Deployment Database Page
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of deployment databases</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetPage([FromQuery] DeploymentDatabaseFilter filter)
        {
            var result = await _deploymentDatabaseService.GetPageAsync(filter);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get Deployment Database Info
        /// </summary>
        /// <param name="id">Database ID</param>
        /// <returns>Database information</returns>
        [HttpGet]
        public async Task<BaseResponse<DeploymentDatabaseInfo>> GetInfo([FromQuery] Guid? id)
        {
            if (!id.HasValue)
                return GetErrorResponse<DeploymentDatabaseInfo>(ApiResult.Error, 400, "Database ID is required");

            var result = await _deploymentDatabaseService.GetInfoAsync(id);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Create or Update Deployment Database
        /// </summary>
        /// <param name="info">Database information</param>
        /// <returns>Validation result</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate>> SetInfo([FromBody] DeploymentDatabaseInfo info)
        {
            if (info == null)
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Database information is required");

            var result = await _deploymentDatabaseService.SetInfoAsync(info);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Delete Deployment Database
        /// </summary>
        /// <param name="id">Database ID</param>
        /// <returns>Validation result</returns>
        [HttpDelete]
        public async Task<BaseResponse<BaseValidate>> Delete([FromQuery] Guid? id)
        {
            if (!id.HasValue)
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Database ID is required");

            var result = await _deploymentDatabaseService.DeleteAsync(id);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Create Deployment Database
        /// </summary>
        /// <param name="request">Create request</param>
        /// <returns>Validation result with database ID</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate<Guid?>>> CreateDatabase([FromBody] CreateDeploymentDatabaseRequest request)
        {
            if (request == null)
                return GetErrorResponse<BaseValidate<Guid?>>(ApiResult.Error, 400, "Create request is required");

            var result = await _deploymentDatabaseService.CreateDeploymentDatabaseAsync(request);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Update Deployment Database
        /// </summary>
        /// <param name="request">Update request</param>
        /// <returns>Validation result</returns>
        [HttpPut]
        public async Task<BaseResponse<BaseValidate>> UpdateDatabase([FromBody] UpdateDeploymentDatabaseRequest request)
        {
            if (request == null)
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Update request is required");

            var result = await _deploymentDatabaseService.UpdateDeploymentDatabaseAsync(request);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get Deployment Databases by Customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="productId">Optional product ID filter</param>
        /// <returns>List of deployment databases</returns>
        [HttpGet]
        public async Task<BaseResponse<IEnumerable<DeploymentDatabaseDto>>> GetByCustomer([FromQuery] Guid customerId, [FromQuery] Guid? productId = null)
        {
            if (customerId == Guid.Empty)
                return GetErrorResponse<IEnumerable<DeploymentDatabaseDto>>(ApiResult.Error, 400, "Customer ID is required");

            var result = await _deploymentDatabaseService.GetByCustomerAsync(customerId, productId);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get Deployment Databases by Deployment
        /// </summary>
        /// <param name="deploymentId">Deployment ID</param>
        /// <returns>List of deployment databases</returns>
        [HttpGet]
        public async Task<BaseResponse<IEnumerable<DeploymentDatabaseDto>>> GetByDeployment([FromQuery] Guid deploymentId)
        {
            if (deploymentId == Guid.Empty)
                return GetErrorResponse<IEnumerable<DeploymentDatabaseDto>>(ApiResult.Error, 400, "Deployment ID is required");

            var result = await _deploymentDatabaseService.GetByDeploymentAsync(deploymentId);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Initialize Database Schema
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <param name="versionId">Schema version ID</param>
        /// <returns>Validation result</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate>> InitializeSchema([FromQuery] Guid databaseId, [FromQuery] Guid versionId)
        {
            if (databaseId == Guid.Empty)
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Database ID is required");

            if (versionId == Guid.Empty)
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Version ID is required");

            var result = await _deploymentDatabaseService.InitializeDatabaseSchemaAsync(databaseId, versionId);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Migrate Database Schema
        /// </summary>
        /// <param name="request">Migration request</param>
        /// <returns>Validation result</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate>> MigrateSchema([FromBody] DatabaseSchemaMigrationRequest request)
        {
            if (request == null)
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Migration request is required");

            var result = await _deploymentDatabaseService.MigrateDatabaseSchemaAsync(request);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Check Database Connection
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <returns>Validation result</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate>> CheckConnection([FromQuery] Guid databaseId)
        {
            if (databaseId == Guid.Empty)
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Database ID is required");

            var result = await _deploymentDatabaseService.CheckConnectionAsync(databaseId);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Update Database Status
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <param name="status">New status</param>
        /// <returns>Validation result</returns>
        [HttpPut]
        public async Task<BaseResponse<BaseValidate>> UpdateStatus([FromQuery] Guid databaseId, [FromQuery] string status)
        {
            if (databaseId == Guid.Empty)
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Database ID is required");

            if (string.IsNullOrWhiteSpace(status))
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Status is required");

            var result = await _deploymentDatabaseService.UpdateStatusAsync(databaseId, status);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get Database Statistics
        /// </summary>
        /// <param name="customerId">Optional customer ID filter</param>
        /// <param name="productId">Optional product ID filter</param>
        /// <returns>Database statistics</returns>
        [HttpGet]
        public async Task<BaseResponse<object>> GetStatistics([FromQuery] Guid? customerId = null, [FromQuery] Guid? productId = null)
        {
            var result = await _deploymentDatabaseService.GetStatisticsAsync(customerId, productId);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Provision Database for Deployment
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="productId">Product ID</param>
        /// <param name="deploymentId">Optional deployment ID</param>
        /// <returns>Validation result with database information</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate<DeploymentDatabaseDto>>> ProvisionDatabase(
            [FromQuery] Guid customerId,
            [FromQuery] Guid productId,
            [FromQuery] Guid? deploymentId = null)
        {
            if (customerId == Guid.Empty)
                return GetErrorResponse<BaseValidate<DeploymentDatabaseDto>>(ApiResult.Error, 400, "Customer ID is required");

            if (productId == Guid.Empty)
                return GetErrorResponse<BaseValidate<DeploymentDatabaseDto>>(ApiResult.Error, 400, "Product ID is required");

            var result = await _deploymentDatabaseService.ProvisionDatabaseAsync(customerId, productId, deploymentId);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Backup Database
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <param name="backupName">Optional backup name</param>
        /// <returns>Validation result</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate>> BackupDatabase([FromQuery] Guid databaseId, [FromQuery] string backupName = null)
        {
            if (databaseId == Guid.Empty)
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Database ID is required");

            var result = await _deploymentDatabaseService.BackupDatabaseAsync(databaseId, backupName);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Restore Database from Backup
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <param name="backupName">Backup name</param>
        /// <returns>Validation result</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate>> RestoreDatabase([FromQuery] Guid databaseId, [FromQuery] string backupName)
        {
            if (databaseId == Guid.Empty)
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Database ID is required");

            if (string.IsNullOrWhiteSpace(backupName))
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 400, "Backup name is required");

            var result = await _deploymentDatabaseService.RestoreDatabaseAsync(databaseId, backupName);
            return GetResponse(ApiResult.Success, result);
        }
    }
}
